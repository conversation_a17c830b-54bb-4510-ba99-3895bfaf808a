import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useProjects } from '@/contexts/ProjectContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { toast } from '@/components/ui/use-toast';
import { motion } from 'framer-motion';
import {
  ArrowLeft, MapPin, Calendar, DollarSign, Users, Edit, MessageSquare, Image as ImageIcon, FileText, TrendingUp, Award, Edit2, SlidersHorizontal, Banknote, Percent, Save, X, Trash2, Plus, Loader2
} from 'lucide-react';
import { generateId } from '@/utils/storage';

const ProjectDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const {
    getProjectById,
    getProjectByIdSync,
    getDailyLogsForProject,
    getMediaForProject,
    getProjectBudgets,
    updateProject,
    updateProjectFinancials,
    addProjectPhase,
    deleteProjectPhase,
    updateProjectPhase,
    addNotification,
    loadInitialData,
    projects,
    loading: contextLoading
  } = useProjects();

  const [project, setProject] = useState(null);
  const [projectLoading, setProjectLoading] = useState(true);

  // Load project data
  useEffect(() => {
    const loadProject = async () => {
      if (!id) return;

      setProjectLoading(true);
      try {
        console.log('Loading project with ID:', id);

        // Always try the async method which handles both local and API fetching
        const projectData = await getProjectById(id);

        console.log('Project data received:', projectData ? 'Found' : 'Not found');
        setProject(projectData);

        if (!projectData) {
          console.log('Project not found for ID:', id);
          toast({
            title: "Project not found",
            description: "The project you're looking for doesn't exist or you don't have access to it.",
            variant: "destructive",
          });
          navigate('/projects');
        }
      } catch (error) {
        console.error('Error loading project:', error);
        toast({
          title: "Error loading project",
          description: "Failed to load project details. Please try again.",
          variant: "destructive",
        });
      } finally {
        setProjectLoading(false);
      }
    };

    loadProject();
  }, [id, getProjectById, navigate]);

  // Also listen for changes in the projects array from context
  useEffect(() => {
    if (projects.length > 0 && id && !project) {
      const contextProject = getProjectByIdSync(id);
      if (contextProject) {
        console.log('Found project in context after context loaded');
        setProject(contextProject);
        setProjectLoading(false);
      }
    }
  }, [projects, id, project, getProjectByIdSync]);

  const dailyLogs = project ? getDailyLogsForProject(id) : [];
  const media = project ? getMediaForProject(id) : [];
  const budgets = project ? getProjectBudgets(id) : [];

  // Calculate total spent from approved budgets
  const calculateProjectSpent = () => {
    return budgets
      .filter(budget => budget.status === 'approved_by_admin' || budget.status === 'approved')
      .reduce((sum, budget) => sum + (budget.totalAmount || 0), 0);
  };

  const projectSpent = calculateProjectSpent();
  // const totalBudgetAmount = budgets.reduce((sum, budget) => sum + (budget.totalAmount || 0), 0);

  const [editingPhaseLabel, setEditingPhaseLabel] = useState({ phaseId: null, phaseName: null, newLabel: '' });
  const [showFinancialsForm, setShowFinancialsForm] = useState(false);
  const [newPhaseName, setNewPhaseName] = useState('');
  const [showAddPhaseForm, setShowAddPhaseForm] = useState(false);
  const [addingPhase, setAddingPhase] = useState(false);
  
  const [financialData, setFinancialData] = useState({
    totalProjectValue: project?.financials?.totalProjectValue || project?.budget || 0,
    initialPayment: project?.financials?.initialPayment || 0,
    outstandingBalance: project?.financials?.outstandingBalance || ((project?.financials?.totalProjectValue || project?.budget || 0) - (project?.financials?.initialPayment || 0)),
    installments: project?.financials?.installments && project.financials.installments.length > 0 
        ? project.financials.installments 
        : [{ amount: 0, dueDate: '', status: 'pending' }]
  });

  if (projectLoading) {
    return (
      <div className="p-4 text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Loading Project...</h2>
        <p className="text-gray-600 mb-4">Please wait while we fetch the project details.</p>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="p-4 text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Project Not Found</h2>
        <p className="text-gray-600 mb-4">The project you're looking for doesn't exist or you don't have access.</p>
        <Link to="/projects">
          <Button>Back to Projects</Button>
        </Link>
      </div>
    );
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'on_hold': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPhaseStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'status-completed';
      case 'in_progress': return 'status-in-progress';
      case 'on_hold': return 'status-on-hold';
      default: return 'status-not-started';
    }
  };
  
  const totalBudgetAmount = budgets.reduce((sum, budget) => sum + (Number(budget.totalAmount) || 0), 0);
  const canEditProject = user?.role === 'admin' || user?.role === 'project_manager' || (user?.role === 'project_lead' && (project.projectLead?.name || project.projectLead) === user.name);
  const canManageFinancials = user?.role === 'admin' || user?.role === 'project_manager' || user?.role === 'accountant';

  const handlePhaseLabelEdit = (phase) => {
    setEditingPhaseLabel({ 
      phaseId: phase.id, 
      phaseName: phase.name, 
      newLabel: phase.customLabel || phase.name 
    });
  };

  const handlePhaseLabelSave = async (phase) => {
    if (!editingPhaseLabel.newLabel.trim()) {
      toast({ title: "Phase label cannot be empty", variant: "destructive" });
      return;
    }

    try {
      // Update the phase via API
      await updateProjectPhase(project.id, phase.id, {
        customLabel: editingPhaseLabel.newLabel
      });

      setEditingPhaseLabel({ phaseId: null, phaseName: null, newLabel: '' });
    } catch (error) {
      // Error handling is done in the ProjectContext
      console.error('Error updating phase label:', error);
    }
  };

  const handleAddPhase = async () => {
    if (!newPhaseName.trim()) {
      toast({ title: "Phase name cannot be empty", variant: "destructive" });
      return;
    }
    if (project.phases.find(p => p.name === newPhaseName || p.customLabel === newPhaseName)) {
      toast({ title: "Phase already exists", description: "A phase with this name or label already exists.", variant: "destructive" });
      return;
    }

    setAddingPhase(true);

    try {
      const phaseData = { name: newPhaseName, customLabel: newPhaseName };
      await addProjectPhase(project.id, phaseData);

      // Only reset and hide form on success
      setNewPhaseName('');
      setShowAddPhaseForm(false);
    } catch (error) {
      // Error handling is done in the ProjectContext, so we don't need to do anything here
      // The form stays open so user can try again
    } finally {
      setAddingPhase(false);
    }
  };

  const handleDeleteExistingPhase = async (phaseToDelete) => {
    if (window.confirm(`Are you sure you want to delete the phase "${phaseToDelete.customLabel || phaseToDelete.name}"? This action cannot be undone.`)) {
      try {
        await deleteProjectPhase(project.id, phaseToDelete.id);
        // The toast notification is already handled in the ProjectContext
        // The UI will be updated automatically through the context state
      } catch (error) {
        // Error handling is done in the ProjectContext
        console.error('Error deleting phase:', error);
      }
    }
  };


  const handleFinancialsChange = (field, value, index = null) => {
    if (field === 'installment') {
      const updatedInstallments = financialData.installments.map((inst, i) => 
        i === index ? { ...inst, [Object.keys(value)[0]]: Object.values(value)[0] } : inst
      );
      setFinancialData(prev => ({ ...prev, installments: updatedInstallments }));
    } else {
      const newValue = parseFloat(value) || 0;
      setFinancialData(prev => {
        const newFinanceState = { ...prev, [field]: newValue };
        if (field === 'totalProjectValue' || field === 'initialPayment') {
            newFinanceState.outstandingBalance = (newFinanceState.totalProjectValue || 0) - (newFinanceState.initialPayment || 0);
        }
        return newFinanceState;
      });
    }
  };

  const addInstallment = () => {
    setFinancialData(prev => ({
      ...prev,
      installments: [...prev.installments, { amount: 0, dueDate: '', status: 'pending' }]
    }));
  };

  const removeInstallment = (index) => {
    setFinancialData(prev => ({
      ...prev,
      installments: prev.installments.filter((_, i) => i !== index)
    }));
  };

  const handleSaveFinancials = () => {
    updateProjectFinancials(project.id, financialData);
    addNotification({
        title: 'Project Financials Updated',
        message: `Financial details for project "${project.title}" have been updated.`,
        type: 'financial',
        projectId: project.id,
        recipientRoles: ['admin', 'project_manager', 'accountant']
    });
    toast({ title: "Financials Updated" });
    setShowFinancialsForm(false);
  };

  const clientFinancialView = (
    <Card className="glass border-white/20">
      <CardHeader><CardTitle>Project Financials</CardTitle></CardHeader>
      <CardContent className="space-y-3">
        <div><Label className="text-gray-600">Total Project Amount:</Label> <p className="font-semibold text-lg">₦{(project.financials?.totalProjectValue || 0).toLocaleString()}</p></div>
        <div><Label className="text-gray-600">Amount Paid:</Label> <p className="font-semibold text-lg">₦{(project.financials?.initialPayment || 0).toLocaleString()}</p></div>
        <div><Label className="text-gray-600">Outstanding Balance:</Label> <p className="font-semibold text-lg text-red-600">₦{(project.financials?.outstandingBalance || 0).toLocaleString()}</p></div>
        {project.financials?.installments && project.financials.installments.length > 0 && (
          <div>
            <Label className="text-gray-600 block mb-1">Payment Installments:</Label>
            {project.financials.installments.map((inst, idx) => (
              <div key={idx} className="text-sm p-2 border rounded-md mb-1 bg-gray-50">
                Installment {idx + 1}: ₦{(inst.amount || 0).toLocaleString()} - Due: {inst.dueDate ? new Date(inst.dueDate).toLocaleDateString() : 'N/A'} - Status: <span className="capitalize font-medium">{inst.status}</span>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );

  return (
    <div className="p-4 max-w-6xl mx-auto space-y-6">
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="space-y-4">
        {/* Mobile-first responsive header */}
        <div className="space-y-3 md:space-y-0">
          {/* Top row: Back button and title */}
          <div className="flex items-start space-x-3">
            <Link to="/projects" className="flex-shrink-0 mt-1">
              <Button variant="ghost" size="icon">
                <ArrowLeft className="h-5 w-5" />
              </Button>
            </Link>
            <div className="min-w-0 flex-1">
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900 break-words leading-tight">
                {project.title}
              </h1>
              <p className="text-gray-600 text-sm md:text-base mt-1">{project.id}</p>
            </div>
          </div>

          {/* Bottom row: Status and edit button - mobile stacked, desktop inline */}
          <div className="flex flex-col space-y-3 md:flex-row md:items-center md:justify-between md:space-y-0 md:pl-12">
            {/* Status badge */}
            <div className="flex justify-start">
              <span className={`inline-flex px-3 py-1.5 rounded-full text-sm font-medium ${getStatusColor(project.status)}`}>
                {project.status.replace('_', ' ').toUpperCase()}
              </span>
            </div>

            {/* Edit button */}
            {canEditProject && (
              <div className="flex justify-start md:justify-end">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigate(`/projects/${project.id}/edit`)}
                  className="w-auto"
                >
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Project
                </Button>
              </div>
            )}
          </div>
        </div>
      </motion.div>

      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.1 }}>
        <Card className="glass border-white/20">
          <CardHeader><CardTitle>Project Overview</CardTitle></CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
              <div><div className="flex items-center text-sm text-gray-600"><MapPin className="mr-2 h-4 w-4" />Location</div><p className="font-medium">{project.location}</p></div>
              <div><div className="flex items-center text-sm text-gray-600"><Calendar className="mr-2 h-4 w-4" />Timeline</div><p className="font-medium">{new Date(project.startDate).toLocaleDateString()} - {new Date(project.endDate).toLocaleDateString()}</p></div>
              { user?.role !== 'client' && (
                <div><div className="flex items-center text-sm text-gray-600"><DollarSign className="mr-2 h-4 w-4" />Budget (Approved)</div><p className="font-medium">₦{(Number(project.budget) / 1000000).toFixed(1)}M</p><p className="text-sm text-gray-500">Spent: ₦{(Number(projectSpent) / 1000000).toFixed(1)}M ({Number(project.budget) > 0 ? ((Number(projectSpent) / Number(project.budget)) * 100).toFixed(0) : 0}%)</p></div>
              )}
               { user?.role === 'client' && (
                <div><div className="flex items-center text-sm text-gray-600"><DollarSign className="mr-2 h-4 w-4" />Project Value</div><p className="font-medium">₦{(Number(project.financials?.totalProjectValue || 0) / 1000000).toFixed(1)}M</p></div>
              )}
              <div><div className="flex items-center text-sm text-gray-600"><Users className="mr-2 h-4 w-4" />Team</div><p className="font-medium">{project.projectLead?.name || project.projectLead}</p><p className="text-sm text-gray-500">{project.teamMembers?.length || 0} members</p></div>
            </div>
            <div className="space-y-2"><div className="flex items-center justify-between"><span className="text-sm font-medium">Overall Progress</span><span className="text-sm font-bold">{project.progress}%</span></div><Progress value={project.progress} className="h-3" /></div>
            <div className="mt-4"><p className="text-gray-700">{project.description}</p></div>
            {project.status === 'completed' && user?.role === 'client' && (
              <div className="mt-4 pt-4 border-t"><Link to={`/testimonials`}><Button className="w-full gradient-accent"><Award className="mr-2 h-4 w-4" />Leave a Testimonial</Button></Link></div>
            )}
          </CardContent>
        </Card>
      </motion.div>

      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.2 }}>
        <Card className="glass border-white/20">
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Project Phases</CardTitle>
            {canEditProject && (
              <Button size="sm" variant="outline" onClick={() => setShowAddPhaseForm(!showAddPhaseForm)}>
                <Plus className="mr-2 h-4 w-4" /> Add Phase
              </Button>
            )}
          </CardHeader>
          <CardContent>
            {showAddPhaseForm && (
              <div className="flex items-center space-x-2 mb-4 p-3 border rounded-md bg-gray-50">
                <Input
                  placeholder="New phase name (e.g., Phase 4)"
                  value={newPhaseName}
                  onChange={(e) => setNewPhaseName(e.target.value)}
                  className="h-9"
                  disabled={addingPhase}
                />
                <Button
                  size="sm"
                  onClick={handleAddPhase}
                  className="gradient-primary text-white"
                  disabled={addingPhase}
                >
                  {addingPhase ? (
                    <>
                      <Loader2 className="mr-1 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-1 h-4 w-4" />
                      Save
                    </>
                  )}
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setShowAddPhaseForm(false)}
                  disabled={addingPhase}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
              {project.phases?.map((phase) => (
                <div key={phase.name} className="space-y-2 p-3 border rounded-lg bg-white/50 shadow-sm">
                  {editingPhaseLabel.phaseName === phase.name ? (
                    <div className="flex items-center space-x-2">
                      <Input value={editingPhaseLabel.newLabel} onChange={(e) => setEditingPhaseLabel(prev => ({ ...prev, newLabel: e.target.value }))} className="h-8 text-sm" />
                      <Button size="icon" variant="ghost" onClick={() => handlePhaseLabelSave(phase.name)} className="h-8 w-8"><Save className="h-4 w-4 text-green-600" /></Button>
                      <Button size="icon" variant="ghost" onClick={() => setEditingPhaseLabel({ phaseName: null, newLabel: '' })} className="h-8 w-8"><X className="h-4 w-4 text-red-600" /></Button>
                    </div>
                  ) : (
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium text-sm text-gray-800">{phase.customLabel || phase.name}</h3>
                      {canEditProject && (
                        <div className="flex items-center">
                          <Button variant="ghost" size="icon" className="h-6 w-6 p-0" onClick={() => handlePhaseLabelEdit(phase)}>
                            <Edit2 className="h-3 w-3 text-gray-500 hover:text-blue-600" />
                          </Button>
                          <Button variant="ghost" size="icon" className="h-6 w-6 p-0" onClick={() => handleDeleteExistingPhase(phase)}>
                            <Trash2 className="h-3 w-3 text-red-400 hover:text-red-600" />
                          </Button>
                        </div>
                      )}
                    </div>
                  )}
                  <div className={`px-2 py-1 rounded text-xs text-center ${getPhaseStatusColor(phase.status)}`}>{phase.status.replace('_', ' ').toUpperCase()}</div>
                  <div className="flex justify-between text-xs"><span className="text-gray-600">Progress</span><span>{phase.progress}%</span></div>
                  <Progress value={phase.progress} className="h-1.5" />
                  <Link to={`/projects/${project.id}/phases/${encodeURIComponent(phase.name)}/edit`}>
                    <Button variant="outline" size="sm" className="w-full mt-2 text-xs"><SlidersHorizontal className="mr-1 h-3 w-3" />Manage Tasks</Button>
                  </Link>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.3 }}>
        <Tabs defaultValue="daily_log" className="space-y-4">
          <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4">
            <TabsTrigger value="daily_log">Daily Log</TabsTrigger>
            <TabsTrigger value="media">Media</TabsTrigger>
            <TabsTrigger value={user?.role === 'client' ? "client_financials" : "budget"}>{user?.role === 'client' ? "Financials" : "Budget"}</TabsTrigger>
            <TabsTrigger value="team">Team</TabsTrigger>
          </TabsList>
          
          <TabsContent value="daily_log">
            <Card className="glass border-white/20">
              <CardHeader><div className="flex items-center justify-between"><CardTitle>Recent Daily Logs</CardTitle><Link to={`/daily-log?projectId=${project.id}`}><Button size="sm"><MessageSquare className="mr-2 h-4 w-4" />View All / Add</Button></Link></div></CardHeader>
              <CardContent>
                {(user?.role === 'client' ? dailyLogs.filter(u => u.isClientVisible) : dailyLogs).length > 0 ? (
                  <div className="space-y-4">
                    {(user?.role === 'client' ? dailyLogs.filter(u => u.isClientVisible) : dailyLogs).slice(0, 5).map((log) => (
                      <div key={log.id} className="border-l-4 border-blue-500 pl-4 py-2">
                        <div className="flex items-center justify-between mb-1"><h4 className="font-medium">{log.title}</h4><span className="text-xs text-gray-500">{new Date(log.timestamp).toLocaleDateString()}</span></div>
                        <p className="text-sm text-gray-600">{log.description}</p>
                      </div>
                    ))}
                  </div>
                ) : (<p className="text-gray-500 text-center py-8">No daily logs yet</p>)}
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="media">
            <Card className="glass border-white/20">
              <CardHeader><div className="flex items-center justify-between"><CardTitle>Project Media</CardTitle><Link to={`/media?projectId=${project.id}`}><Button size="sm"><ImageIcon className="mr-2 h-4 w-4" />View All / Upload</Button></Link></div></CardHeader>
              <CardContent>
                {(user?.role === 'client' ? media.filter(m => m.isClientVisible) : media).length > 0 ? (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {(user?.role === 'client' ? media.filter(m => m.isClientVisible) : media).slice(0, 8).map((item) => (
                      <div key={item.id} className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center"><ImageIcon className="h-8 w-8 text-gray-400" /></div>
                    ))}
                  </div>
                ) : (<p className="text-gray-500 text-center py-8">No media uploaded yet</p>)}
              </CardContent>
            </Card>
          </TabsContent>
          
          {user?.role === 'client' ? (
            <TabsContent value="client_financials">{clientFinancialView}</TabsContent>
          ) : (
            <TabsContent value="budget">
              <Card className="glass border-white/20">
                <CardHeader><div className="flex items-center justify-between"><CardTitle>Budget Summary</CardTitle><Link to={`/projects/${id}/budget`}><Button size="sm"><TrendingUp className="mr-2 h-4 w-4" />Manage Budgets</Button></Link></div></CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="p-4 bg-blue-50 rounded-lg">
                        <p className="font-medium text-blue-800">Total Submitted</p>
                        <p className="text-2xl font-bold text-blue-600">₦{(totalBudgetAmount / 1000000).toFixed(1)}M</p>
                      </div>
                      <div className="p-4 bg-green-50 rounded-lg">
                        <p className="font-medium text-green-800">Approved & Spent</p>
                        <p className="text-2xl font-bold text-green-600">₦{(projectSpent / 1000000).toFixed(1)}M</p>
                      </div>
                      <div className="p-4 bg-gray-50 rounded-lg">
                        <p className="font-medium text-gray-800">Project Budget</p>
                        <p className="text-2xl font-bold text-gray-600">₦{(Number(project.budget) / 1000000).toFixed(1)}M</p>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Overall Budget Utilization (Approved)</span>
                        <span>{Number(project.budget) > 0 ? ((Number(projectSpent) / Number(project.budget)) * 100).toFixed(0) : 0}%</span>
                      </div>
                      <Progress value={Number(project.budget) > 0 ? (Number(projectSpent) / Number(project.budget)) * 100 : 0} className="h-2" />
                      <div className="text-xs text-gray-500 mt-2">
                        Approved budgets: ₦{(projectSpent / 1000000).toFixed(1)}M of ₦{(Number(project.budget) / 1000000).toFixed(1)}M total budget
                      </div>
                    </div>

                    {/* Budget Status Summary */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                      <div className="text-center p-2 bg-green-100 rounded">
                        <div className="font-semibold text-green-800">{budgets.filter(b => b.status === 'approved_by_admin' || b.status === 'approved').length}</div>
                        <div className="text-green-600">Approved</div>
                      </div>
                      <div className="text-center p-2 bg-yellow-100 rounded">
                        <div className="font-semibold text-yellow-800">{budgets.filter(b => b.status === 'pending_admin_approval' || b.status === 'pending_admin').length}</div>
                        <div className="text-yellow-600">Pending</div>
                      </div>
                      <div className="text-center p-2 bg-orange-100 rounded">
                        <div className="font-semibold text-orange-800">{budgets.filter(b => b.status === 'revision_needed').length}</div>
                        <div className="text-orange-600">Revision</div>
                      </div>
                      <div className="text-center p-2 bg-red-100 rounded">
                        <div className="font-semibold text-red-800">{budgets.filter(b => b.status === 'rejected').length}</div>
                        <div className="text-red-600">Rejected</div>
                      </div>
                    </div>
                    {budgets.length > 0 ? (<div className="mt-4 space-y-2"><h4 className="font-medium">Recent Budget Submissions:</h4>{budgets.slice(0,3).map(b => (<div key={b.id} className="text-sm p-2 border rounded-md">ID: {b.id} - Amount: ₦{typeof b.totalAmount === 'number' ? b.totalAmount.toLocaleString() : 'N/A'} - Status: <span className="font-semibold">{b.status.replace(/_/g, ' ')}</span></div>))}</div>) : (<p className="text-gray-500 text-center py-4">No detailed budget submissions yet.</p>)}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          )}

          {canManageFinancials && user?.role !== 'client' && (
             <Link to={`/projects/${id}/financials`}>
                <Button variant="outline" className="w-full mt-4">
                    <Banknote className="mr-2 h-4 w-4" /> Manage Project Financials (Admin/Accountant)
                </Button>
            </Link>
          )}
          
          <TabsContent value="team">
            <Card className="glass border-white/20">
              <CardHeader><CardTitle>Team Members</CardTitle></CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 border rounded-lg bg-blue-50"><div className="flex items-center space-x-3"><Avatar className="w-10 h-10"><AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${project.projectLead?.name || project.projectLead}`} /><AvatarFallback>{(project.projectLead?.name || project.projectLead)?.split(' ').map(n=>n[0]).join('') || 'PL'}</AvatarFallback></Avatar><div><p className="font-medium">{project.projectLead?.name || project.projectLead}</p><p className="text-sm text-blue-700">Project Lead</p></div></div></div>
                  {project.teamMembers?.map((member, index) => {
                    const memberName = member.user?.name || member.name || member;
                    return (
                      <div key={member.user?.id || index} className="p-4 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <Avatar className="w-10 h-10">
                            <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${memberName}`} />
                            <AvatarFallback>{memberName?.split(' ').map(n=>n[0]).join('') || 'TM'}</AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">{memberName}</p>
                            <p className="text-sm text-gray-600">{member.user?.role?.replace('_', ' ') || 'Team Member'}</p>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </motion.div>
    </div>
  );
};

export default ProjectDetail;
