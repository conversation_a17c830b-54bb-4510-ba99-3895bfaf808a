import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';
import { motion } from 'framer-motion';
import { ArrowLeft, Save, UserPlus, Mail, Phone, Briefcase, Lock, PackageSearch, Calculator } from 'lucide-react';

const AddStaff = () => {
  const navigate = useNavigate();
  const { createUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    phone: '',
    role: 'team_member', 
    location: '',
    company: 'Karmod Nigeria' 
  });

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    if (!formData.role) {
      toast({ title: "Role Required", description: "Please select a role for the staff member.", variant: "destructive" });
      setLoading(false);
      return;
    }

    try {
      const result = await createUser(formData);
      if (result.success) {
        toast({ title: "Staff Added Successfully!", description: `${formData.name} has been added to the system.` });
        navigate('/admin/staff');
      } else {
        toast({ title: "Error Adding Staff", description: result.error || "Please try again.", variant: "destructive" });
      }
    } catch (error) {
      toast({ title: "Error Adding Staff", description: "Please try again.", variant: "destructive" });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="p-4 max-w-2xl mx-auto space-y-6">
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="flex items-center space-x-4">
        <Button variant="ghost" size="icon" onClick={() => navigate('/admin/staff')}><ArrowLeft className="h-5 w-5" /></Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Add New Staff Member</h1>
          <p className="text-gray-600">Enter the details for the new staff.</p>
        </div>
      </motion.div>

      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.1 }}>
        <Card className="glass border-white/20">
          <CardHeader>
            <CardTitle>Staff Information</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="name"><UserPlus className="inline mr-2 h-4 w-4" />Full Name *</Label>
                <Input id="name" placeholder="Enter full name" value={formData.name} onChange={(e) => handleInputChange('name', e.target.value)} required />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="email"><Mail className="inline mr-2 h-4 w-4" />Email Address *</Label>
                  <Input id="email" type="email" placeholder="Enter email" value={formData.email} onChange={(e) => handleInputChange('email', e.target.value)} required />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone"><Phone className="inline mr-2 h-4 w-4" />Phone Number</Label>
                  <Input id="phone" type="tel" placeholder="Enter phone number" value={formData.phone} onChange={(e) => handleInputChange('phone', e.target.value)} />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="password"><Lock className="inline mr-2 h-4 w-4" />Password *</Label>
                <Input id="password" type="password" placeholder="Enter initial password" value={formData.password} onChange={(e) => handleInputChange('password', e.target.value)} required />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="role"><Briefcase className="inline mr-2 h-4 w-4" />Role *</Label>
                  <Select value={formData.role} onValueChange={(value) => handleInputChange('role', value)} required>
                    <SelectTrigger><SelectValue placeholder="Select role" /></SelectTrigger>
                    <SelectContent>
                      <SelectItem value="team_member">Site Engineer / Team Member</SelectItem>
                      <SelectItem value="project_lead">Project Lead</SelectItem>
                      <SelectItem value="project_manager">Project Manager</SelectItem>
                      <SelectItem value="accountant">Accountant</SelectItem>
                      <SelectItem value="store_keeper">Store Keeper</SelectItem>
                      <SelectItem value="quantity_surveyor">Quantity Surveyor</SelectItem>
                      <SelectItem value="admin">Admin</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="location">Location</Label>
                  <Input id="location" placeholder="Enter location" value={formData.location} onChange={(e) => handleInputChange('location', e.target.value)} />
                </div>
              </div>
               <div className="space-y-2">
                  <Label htmlFor="company">Company</Label>
                  <Input id="company" placeholder="Karmod Nigeria" value={formData.company} onChange={(e) => handleInputChange('company', e.target.value)} />
                </div>
              <div className="flex justify-end space-x-4 pt-6">
                <Button type="button" variant="outline" onClick={() => navigate('/admin/staff')}>Cancel</Button>
                <Button type="submit" className="gradient-primary text-white" disabled={loading}>
                  <Save className="mr-2 h-4 w-4" />{loading ? 'Adding...' : 'Add Staff Member'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default AddStaff;