import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { motion } from 'framer-motion';
import { Users, Briefcase, Mail, Phone, Plus, Edit, PackageSearch, Calculator, Trash2, MoreVertical } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import ErrorDisplay from '@/components/ui/ErrorDisplay';
import Pagination from '@/components/ui/pagination';
import ConfirmDialog from '@/components/ui/ConfirmDialog';

const StaffList = () => {
  const { getAllStaff, deleteUser } = useAuth();
  const navigate = useNavigate();
  const [staffMembers, setStaffMembers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState(null);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 10
  });

  const loadStaff = async (page = 1) => {
    try {
      setLoading(true);
      setError(null);
      const { users, pagination } = await getAllStaff({ page, limit: 10 });
      setStaffMembers(users || []);
      if (pagination) {
        setPagination(pagination);
      }
    } catch (err) {
      console.error('Error loading staff:', err);
      setError('Failed to load staff members');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadStaff(1);
  }, []);

  const getRoleDisplay = (role) => {
    switch (role) {
      case 'project_lead': return 'Project Lead';
      case 'project_manager': return 'Project Manager';
      case 'team_member': return 'Site Engineer / Team Member';
      case 'accountant': return 'Accountant';
      case 'store_keeper': return 'Store Keeper';
      case 'quantity_surveyor': return 'Quantity Surveyor';
      case 'admin': return 'Administrator';
      default: return role;
    }
  };

  const handleDeleteClick = (user, e) => {
    e.preventDefault();
    e.stopPropagation();
    setUserToDelete(user);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!userToDelete) return;

    const result = await deleteUser(userToDelete.id);
    if (result.success) {
      toast({
        title: "Staff Member Deleted",
        description: `${userToDelete.name} has been removed from the system.`,
      });
      // Reload the current page to refresh the list
      loadStaff(pagination.currentPage);
    } else {
      toast({
        title: "Delete Failed",
        description: result.error || "Failed to delete staff member. Please try again.",
        variant: "destructive",
      });
    }

    setDeleteDialogOpen(false);
    setUserToDelete(null);
  };

  const handlePageChange = (page) => {
    loadStaff(page);
  };

  if (loading) {
    return (
      <div className="p-4">
        <LoadingSpinner size="lg" text="Loading staff members..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4">
        <ErrorDisplay
          error={error}
          title="Failed to Load Staff"
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  return (
    <div className="p-4 space-y-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0"
      >
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Staff Management</h1>
          <p className="text-gray-600">View and manage all staff members.</p>
        </div>
        <Button onClick={() => navigate('/admin/staff/new')} className="gradient-primary text-white">
          <Plus className="mr-2 h-4 w-4" /> Add New Staff
        </Button>
      </motion.div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {staffMembers.map((staff, index) => (
          <motion.div
            key={staff.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.05 }}
          >
            <Card className="glass border-white/20 hover:shadow-xl transition-shadow duration-300">
              <CardHeader className="flex flex-row items-center space-x-4 pb-3">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${staff.name}`} />
                  <AvatarFallback>{staff.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <CardTitle className="text-lg">{staff.name}</CardTitle>
                  <CardDescription>{getRoleDisplay(staff.role)}</CardDescription>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 hover:bg-gray-100"
                    >
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      onClick={(e) => handleDeleteClick(staff, e)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50 disabled"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete Staff
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </CardHeader>
              <CardContent className="space-y-2 text-sm text-gray-700">
                <div className="flex items-center">
                  <Mail className="mr-2 h-4 w-4 text-gray-500" /> {staff.email}
                </div>
                {staff.phone && (
                  <div className="flex items-center">
                    <Phone className="mr-2 h-4 w-4 text-gray-500" /> {staff.phone}
                  </div>
                )}
                <div className="pt-2 flex space-x-2">
                  <Link to={`/admin/staff/${encodeURIComponent(staff.name)}/projects`} className="flex-1">
                    <Button variant="outline" size="sm" className="w-full">
                      <Briefcase className="mr-2 h-4 w-4" /> Projects
                    </Button>
                  </Link>
                  <Link to={`/admin/staff/edit/${staff.id}`} className="flex-1">
                     <Button variant="secondary" size="sm" className="w-full">
                       <Edit className="mr-2 h-4 w-4" /> Edit
                     </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
      {staffMembers.length === 0 && (
        <motion.p initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="text-center text-gray-500 py-8">
          No staff members found.
        </motion.p>
      )}

      {staffMembers.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mt-6"
        >
          <Pagination
            currentPage={pagination.currentPage}
            totalPages={pagination.totalPages}
            totalItems={pagination.totalItems}
            itemsPerPage={pagination.itemsPerPage}
            onPageChange={handlePageChange}
          />
        </motion.div>
      )}

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title="Delete Staff Member"
        description={`Are you sure you want to delete "${userToDelete?.name}"? This action cannot be undone and will remove all associated data.`}
        confirmText="Delete Staff"
        onConfirm={handleDeleteConfirm}
      />
    </div>
  );
};

export default StaffList;