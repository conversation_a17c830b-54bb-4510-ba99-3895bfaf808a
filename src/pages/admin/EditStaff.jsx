import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';
import { motion } from 'framer-motion';
import { ArrowLeft, Save, UserCog, Mail, Phone, Briefcase, Lock, PackageSearch, Calculator } from 'lucide-react';

const EditStaff = () => {
  const navigate = useNavigate();
  const { userId } = useParams();
  const { getUserById, updateUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState(null);

  useEffect(() => {
    const loadStaffMember = async () => {
      try {
        const staffMember = await getUserById(userId);
        if (staffMember) {
          setFormData({ ...staffMember, password: '' });
        } else {
          toast({ title: "Error", description: "Staff member not found.", variant: "destructive" });
          navigate('/admin/staff');
        }
      } catch (error) {
        console.error('Error loading staff member:', error);
        toast({ title: "Error", description: "Failed to load staff member data.", variant: "destructive" });
        navigate('/admin/staff');
      }
    };

    loadStaffMember();
  }, [userId, getUserById, navigate]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    if (!formData.role) {
      toast({ title: "Role Required", description: "Please select a role for the staff member.", variant: "destructive" });
      setLoading(false);
      return;
    }

    try {
      const dataToUpdate = { ...formData };

      // Remove fields that shouldn't be sent in update request
      delete dataToUpdate.id;
      delete dataToUpdate.createdAt;
      delete dataToUpdate.updatedAt;
      delete dataToUpdate.avatar;

      // Remove password if empty
      if (!dataToUpdate.password) {
        delete dataToUpdate.password;
      }

      // Ensure string fields are properly handled
      const stringFields = ['phone', 'location', 'company', 'bio'];
      stringFields.forEach(field => {
        if (dataToUpdate[field] === null || dataToUpdate[field] === undefined) {
          dataToUpdate[field] = '';
        }
      });

      const result = await updateUser(userId, dataToUpdate);
      if (result.success) {
        toast({ title: "Staff Updated Successfully!", description: `${formData.name}'s details have been updated.` });
        navigate('/admin/staff');
      } else {
        toast({ title: "Error Updating Staff", description: result.error || "Please try again.", variant: "destructive" });
      }
    } catch (error) {
      toast({ title: "Error Updating Staff", description: "Please try again.", variant: "destructive" });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  if (!formData) {
    return <div className="p-4 text-center">Loading staff data...</div>;
  }

  return (
    <div className="p-4 max-w-2xl mx-auto space-y-6">
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="flex items-center space-x-4">
        <Button variant="ghost" size="icon" onClick={() => navigate('/admin/staff')}><ArrowLeft className="h-5 w-5" /></Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Edit Staff Member</h1>
          <p className="text-gray-600">Update details for {formData.name}.</p>
        </div>
      </motion.div>

      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.1 }}>
        <Card className="glass border-white/20">
          <CardHeader>
            <CardTitle>Staff Information</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="name"><UserCog className="inline mr-2 h-4 w-4" />Full Name *</Label>
                <Input id="name" placeholder="Enter full name" value={formData.name} onChange={(e) => handleInputChange('name', e.target.value)} required />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="email"><Mail className="inline mr-2 h-4 w-4" />Email Address *</Label>
                  <Input id="email" type="email" placeholder="Enter email" value={formData.email} onChange={(e) => handleInputChange('email', e.target.value)} required />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone"><Phone className="inline mr-2 h-4 w-4" />Phone Number</Label>
                  <Input id="phone" type="tel" placeholder="Enter phone number" value={formData.phone} onChange={(e) => handleInputChange('phone', e.target.value)} />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="password"><Lock className="inline mr-2 h-4 w-4" />Password</Label>
                <Input id="password" type="password" placeholder="Leave blank to keep current password" value={formData.password} onChange={(e) => handleInputChange('password', e.target.value)} />
                <p className="text-xs text-gray-500">Leave blank if you don't want to change the password.</p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="role"><Briefcase className="inline mr-2 h-4 w-4" />Role *</Label>
                  <Select value={formData.role} onValueChange={(value) => handleInputChange('role', value)} required>
                    <SelectTrigger><SelectValue placeholder="Select role" /></SelectTrigger>
                    <SelectContent>
                      <SelectItem value="team_member">Site Engineer / Team Member</SelectItem>
                      <SelectItem value="project_lead">Project Lead</SelectItem>
                      <SelectItem value="project_manager">Project Manager</SelectItem>
                      <SelectItem value="accountant">Accountant</SelectItem>
                      <SelectItem value="store_keeper">Store Keeper</SelectItem>
                      <SelectItem value="quantity_surveyor">Quantity Surveyor</SelectItem>
                      <SelectItem value="admin">Admin</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="location">Location</Label>
                  <Input id="location" placeholder="Enter location" value={formData.location} onChange={(e) => handleInputChange('location', e.target.value)} />
                </div>
              </div>
               <div className="space-y-2">
                  <Label htmlFor="company">Company</Label>
                  <Input id="company" placeholder="Karmod Nigeria" value={formData.company} onChange={(e) => handleInputChange('company', e.target.value)} />
                </div>
              <div className="flex justify-end space-x-4 pt-6">
                <Button type="button" variant="outline" onClick={() => navigate('/admin/staff')}>Cancel</Button>
                <Button type="submit" className="gradient-primary text-white" disabled={loading}>
                  <Save className="mr-2 h-4 w-4" />{loading ? 'Saving...' : 'Save Changes'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default EditStaff;