import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useProjects } from '@/contexts/ProjectContext';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';
import { motion } from 'framer-motion';
import { ArrowLeft, Save, UserPlus, Search, Trash2, Users } from 'lucide-react';

const CreateProject = () => {
  const navigate = useNavigate();
  const { createProject } = useProjects();
  const { getAllStaff, getAllClients } = useAuth();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    location: '',
    category: '',
    startDate: '',
    endDate: '',
    budget: '',
    projectLead: '',
    client: '',
    teamMembers: [] 
  });

  const [allStaffList, setAllStaffList] = useState([]);
  const [projectLeadStaffList, setProjectLeadStaffList] = useState([]);
  const [clientList, setClientList] = useState([]);
  const [loadingData, setLoadingData] = useState(true);

  const [projectLeadSearch, setProjectLeadSearch] = useState('');
  const [clientSearch, setClientSearch] = useState('');
  const [teamMemberSearch, setTeamMemberSearch] = useState('');
  
  const [showProjectLeadDropdown, setShowProjectLeadDropdown] = useState(false);
  const [showClientDropdown, setShowClientDropdown] = useState(false);
  const [showTeamMemberDropdown, setShowTeamMemberDropdown] = useState(false);

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoadingData(true);
        const [staffResponse, clientsResponse] = await Promise.all([
          getAllStaff({ limit: 100 }), // Get more staff for project creation
          getAllClients({ limit: 100 }) // Get more clients for project creation
        ]);

        const staff = staffResponse.users || [];
        const clients = clientsResponse.users || [];

        setAllStaffList(staff);
        setProjectLeadStaffList(staff.filter(s => s.role === 'project_lead' || s.role === 'admin' || s.role === 'project_manager'));
        setClientList(clients);
      } catch (error) {
        console.error('Error loading data:', error);
        toast({
          title: "Error loading data",
          description: "Failed to load staff and client data. Please refresh the page.",
          variant: "destructive",
        });
      } finally {
        setLoadingData(false);
      }
    };

    loadData();
  }, [getAllStaff, getAllClients]);

  const filteredProjectLeads = projectLeadStaffList.filter(staff =>
    staff.name.toLowerCase().includes(projectLeadSearch.toLowerCase())
  );

  const filteredClients = clientList.filter(client =>
    client.name.toLowerCase().includes(clientSearch.toLowerCase()) ||
    (client.company && client.company.toLowerCase().includes(clientSearch.toLowerCase()))
  );

  const availableTeamMembers = allStaffList.filter(
    staff => !formData.teamMembers.find(member => member.id === staff.id) && staff.name.toLowerCase().includes(teamMemberSearch.toLowerCase())
  );


  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    if (!formData.category) {
      toast({ title: "Category Required", description: "Please select a project category.", variant: "destructive" });
      setLoading(false);
      return;
    }
    if (!formData.projectLead) {
      toast({ title: "Project Lead Required", description: "Please select a project lead.", variant: "destructive" });
      setLoading(false);
      return;
    }
    if (!formData.client) {
      toast({ title: "Client Required", description: "Please select a client.", variant: "destructive" });
      setLoading(false);
      return;
    }

    try {
      const projectData = {
        title: formData.title,
        description: formData.description,
        location: formData.location,
        category: formData.category,
        startDate: formData.startDate,
        endDate: formData.endDate,
        budget: parseFloat(formData.budget),
        projectLeadId: formData.projectLead,
        clientId: formData.client,
        teamMemberIds: formData.teamMembers.map(member => member.id)
      };

      const result = await createProject(projectData);

      if (result.success) {
        toast({
          title: "Project created successfully!",
          description: `Project ${result.project.id} has been created.`,
        });

        navigate(`/projects/${result.project.id}`);
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      toast({
        title: "Error creating project",
        description: "Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleProjectLeadSelect = (staff) => {
    setFormData(prev => ({ ...prev, projectLead: staff.id, projectLeadName: staff.name }));
    setProjectLeadSearch(staff.name);
    setShowProjectLeadDropdown(false);
  };

  const handleClientSelect = (client) => {
    setFormData(prev => ({ ...prev, client: client.id, clientName: client.name }));
    setClientSearch(client.name);
    setShowClientDropdown(false);
  };
  
  const addTeamMember = (staff) => {
    if (staff && !formData.teamMembers.find(member => member.id === staff.id)) {
      setFormData(prev => ({
        ...prev,
        teamMembers: [...prev.teamMembers, { id: staff.id, name: staff.name }]
      }));
    }
    setTeamMemberSearch('');
    setShowTeamMemberDropdown(false);
  };

  const removeTeamMember = (memberToRemove) => {
    setFormData(prev => ({
      ...prev,
      teamMembers: prev.teamMembers.filter((member) => member.id !== memberToRemove.id)
    }));
  };


  if (loadingData) {
    return (
      <div className="p-4 max-w-4xl mx-auto">
        <div className="flex items-center space-x-4 mb-6">
          <Button variant="ghost" size="icon" onClick={() => navigate('/projects')}><ArrowLeft className="h-5 w-5" /></Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Create New Project</h1>
            <p className="text-gray-600">Set up a new construction project</p>
          </div>
        </div>
        <div className="flex justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading project data...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 max-w-4xl mx-auto space-y-6">
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="flex items-center space-x-4">
        <Button variant="ghost" size="icon" onClick={() => navigate('/projects')}><ArrowLeft className="h-5 w-5" /></Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Create New Project</h1>
          <p className="text-gray-600">Set up a new construction project</p>
        </div>
      </motion.div>

      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.1 }}>
        <Card className="glass border-white/20">
          <CardHeader>
            <CardTitle>Project Details</CardTitle>
            <CardDescription>Fill in the information below to create your project</CardDescription>
          </CardHeader>
          
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="title">Project Title *</Label>
                  <Input id="title" placeholder="Enter project title" value={formData.title} onChange={(e) => handleInputChange('title', e.target.value)} required />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="category">Category *</Label>
                  <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)} required>
                    <SelectTrigger><SelectValue placeholder="Select category" /></SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Commercial">Commercial</SelectItem>
                      <SelectItem value="Residential">Residential</SelectItem>
                      <SelectItem value="Industrial">Industrial</SelectItem>
                      <SelectItem value="Educational">Educational</SelectItem>
                      <SelectItem value="Government">Government</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description *</Label>
                <Textarea id="description" placeholder="Describe the project scope and objectives" value={formData.description} onChange={(e) => handleInputChange('description', e.target.value)} rows={3} required />
              </div>

              <div className="space-y-2">
                <Label htmlFor="location">Location *</Label>
                <Input id="location" placeholder="Project location (city, state)" value={formData.location} onChange={(e) => handleInputChange('location', e.target.value)} required />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="startDate">Start Date *</Label>
                  <Input id="startDate" type="date" value={formData.startDate} onChange={(e) => handleInputChange('startDate', e.target.value)} required />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="endDate">End Date *</Label>
                  <Input id="endDate" type="date" value={formData.endDate} onChange={(e) => handleInputChange('endDate', e.target.value)} required />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="budget">Budget (₦) *</Label>
                  <Input id="budget" type="number" placeholder="0" value={formData.budget} onChange={(e) => handleInputChange('budget', e.target.value)} required />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2 relative">
                  <Label htmlFor="projectLead">Project Lead *</Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="projectLead"
                      placeholder="Search for project lead"
                      value={projectLeadSearch}
                      onChange={(e) => { setProjectLeadSearch(e.target.value); setShowProjectLeadDropdown(true); }}
                      onFocus={() => setShowProjectLeadDropdown(true)}
                      onBlur={() => setTimeout(() => setShowProjectLeadDropdown(false), 150)}
                      required
                      className="pl-10"
                    />
                  </div>
                  {showProjectLeadDropdown && filteredProjectLeads.length > 0 && (
                    <motion.div initial={{ opacity: 0, y: -10 }} animate={{ opacity: 1, y: 0 }} className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                      {filteredProjectLeads.map(staff => (
                        <div
                          key={staff.id}
                          onMouseDown={(e) => {
                            e.preventDefault();
                            handleProjectLeadSelect(staff);
                          }}
                          className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                        >
                          {staff.name} ({staff.role.replace('_', ' ')})
                        </div>
                      ))}
                    </motion.div>
                  )}
                </div>
                
                <div className="space-y-2 relative">
                  <Label htmlFor="client">Client *</Label>
                   <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="client"
                      placeholder="Search for client or company"
                      value={clientSearch}
                      onChange={(e) => { setClientSearch(e.target.value); setShowClientDropdown(true); }}
                      onFocus={() => setShowClientDropdown(true)}
                      onBlur={() => setTimeout(() => setShowClientDropdown(false), 150)}
                      required
                      className="pl-10"
                    />
                  </div>
                  {showClientDropdown && filteredClients.length > 0 && (
                    <motion.div initial={{ opacity: 0, y: -10 }} animate={{ opacity: 1, y: 0 }} className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                      {filteredClients.map(client => (
                        <div
                          key={client.id}
                          onMouseDown={(e) => {
                            e.preventDefault();
                            handleClientSelect(client);
                          }}
                          className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                        >
                          {client.name} {client.company && `(${client.company})`}
                        </div>
                      ))}
                    </motion.div>
                  )}
                </div>
              </div>
              
              <div className="space-y-2 relative">
                <Label htmlFor="teamMembers"><Users className="inline mr-2 h-4 w-4" />Team Members</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="teamMembers"
                    placeholder="Search to add team members"
                    value={teamMemberSearch}
                    onChange={(e) => { setTeamMemberSearch(e.target.value); setShowTeamMemberDropdown(true); }}
                    onFocus={() => setShowTeamMemberDropdown(true)}
                    onBlur={() => setTimeout(() => setShowTeamMemberDropdown(false), 150)}
                    className="pl-10"
                  />
                </div>
                {showTeamMemberDropdown && availableTeamMembers.length > 0 && (
                  <motion.div initial={{ opacity: 0, y: -10 }} animate={{ opacity: 1, y: 0 }} className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                    {availableTeamMembers.map(staff => (
                      <div
                        key={staff.id}
                        onMouseDown={(e) => {
                          e.preventDefault();
                          addTeamMember(staff);
                        }}
                        className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                      >
                        {staff.name} ({staff.role.replace('_', ' ')})
                      </div>
                    ))}
                  </motion.div>
                )}
                <div className="mt-2 space-x-2 space-y-2">
                  {formData.teamMembers.map((member, index) => (
                    <span key={member.id} className="inline-flex items-center bg-gray-100 text-gray-700 text-sm font-medium px-2.5 py-0.5 rounded-full">
                      {member.name}
                      <Button type="button" variant="ghost" size="icon" onClick={() => removeTeamMember(member)} className="ml-1 h-5 w-5 p-0 text-gray-500 hover:text-red-500">
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </span>
                  ))}
                </div>
              </div>


              <div className="flex justify-end space-x-4 pt-6">
                <Button type="button" variant="outline" onClick={() => navigate('/projects')}>Cancel</Button>
                <Button type="submit" className="gradient-primary text-white" disabled={loading}>
                  <Save className="mr-2 h-4 w-4" />{loading ? 'Creating...' : 'Create Project'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default CreateProject;