import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useProjects } from '@/contexts/ProjectContext';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';
import { motion } from 'framer-motion';
import { Plus, MessageSquare, Filter, FileText, Calendar, X } from 'lucide-react';
import DailyLogForm from '@/components/dailylog/DailyLogForm';
import DailyLogItem from '@/components/dailylog/DailyLogItem';

const DailyLogPage = () => {
  const { user } = useAuth();
  const { projects, dailyLogs, addDailyLog, deleteDailyLogMessage } = useProjects();
  const [showForm, setShowForm] = useState(false);
  
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const initialProjectIdFilter = queryParams.get('projectId') || 'all';
  const [projectIdFilter, setProjectIdFilter] = useState(initialProjectIdFilter);

  // Date filter states
  const [dateFilter, setDateFilter] = useState('all');
  const [customStartDate, setCustomStartDate] = useState('');
  const [customEndDate, setCustomEndDate] = useState('');

  // Clear all filters
  const clearFilters = () => {
    setProjectIdFilter('all');
    setDateFilter('all');
    setCustomStartDate('');
    setCustomEndDate('');
  };

  // Check if any filters are active
  const hasActiveFilters = projectIdFilter !== 'all' || dateFilter !== 'all' || customStartDate || customEndDate;

  const handleFormSubmit = (logData) => {
    addDailyLog(logData);
    toast({
      title: "Daily Log added successfully!",
      description: "Your project daily log has been recorded.",
    });
    setShowForm(false);
  };

  const handleDeleteLogMessage = async (logId, messageId, isReply = false) => {
    if(window.confirm("Are you sure you want to delete this message? This action cannot be undone.")) {
      const result = await deleteDailyLogMessage(logId, messageId, isReply);
      if (result.success) {
        toast({
          title: "Message Deleted",
          description: isReply ? "The reply has been removed." : "The daily log has been removed."
        });
      } else {
        toast({
          title: "Delete Failed",
          description: result.error || "Failed to delete message. Please try again.",
          variant: "destructive"
        });
      }
    }
  };

  const getVisibleProjectsForFilter = () => {
    if (user.role === 'client') {
      return projects.filter(p => p.client === user.name);
    }
    return projects;
  };

  const getDateFilterRange = () => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    switch (dateFilter) {
      case 'today':
        const endOfToday = new Date(today);
        endOfToday.setHours(23, 59, 59, 999);
        return { start: today, end: endOfToday };
      case 'last7days':
        const sevenDaysAgo = new Date(today.getTime() - 6 * 24 * 60 * 60 * 1000); // Include today
        const endOfToday7 = new Date(now);
        endOfToday7.setHours(23, 59, 59, 999);
        return { start: sevenDaysAgo, end: endOfToday7 };
      case 'custom':
        if (customStartDate && customEndDate) {
          const start = new Date(customStartDate);
          start.setHours(0, 0, 0, 0);
          const end = new Date(customEndDate);
          end.setHours(23, 59, 59, 999);
          return { start, end };
        }
        return null;
      default:
        return null;
    }
  };

  const filteredDailyLogs = dailyLogs.filter(log => {
    const projectMatch = projectIdFilter === 'all' || log.projectId === projectIdFilter;

    // Date filtering
    const dateRange = getDateFilterRange();
    let dateMatch = true;
    if (dateRange) {
      // Handle different timestamp formats (ISO string, timestamp, or createdAt)
      const logTimestamp = log.timestamp || log.createdAt;
      const logDate = new Date(logTimestamp);

      // Debug logging (remove in production)
      if (dateFilter !== 'all') {
        console.log('Date filter debug:', {
          dateFilter,
          logTimestamp,
          logDate: logDate.toISOString(),
          start: dateRange.start.toISOString(),
          end: dateRange.end.toISOString(),
          match: logDate >= dateRange.start && logDate <= dateRange.end
        });
      }

      dateMatch = logDate >= dateRange.start && logDate <= dateRange.end;
    }

    if (user?.role === 'client') {
      const project = projects.find(p => p.id === log.projectId);
      return projectMatch && dateMatch && log.isClientVisible && project?.client === user.name;
    }
    return projectMatch && dateMatch;
  }).sort((a,b) => new Date(b.timestamp || b.createdAt) - new Date(a.timestamp || a.createdAt));

  return (
    <div className="p-4 space-y-6">
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Daily Logs</h1>
          <p className="text-gray-600 dark:text-gray-400">Track progress and communicate with your team</p>
        </div>
        <div className="bg-white rounded-lg border p-4 space-y-4">
          {/* Filter Header */}
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-gray-700 flex items-center">
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </h3>
            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="h-4 w-4 mr-1" />
                Clear All
              </Button>
            )}
          </div>

          {/* Filter Controls */}
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Project Filter */}
            <div className="flex-1 min-w-0">
              <label className="block text-xs font-medium text-gray-600 mb-1">Project</label>
              <Select value={projectIdFilter} onValueChange={setProjectIdFilter}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="All Projects" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Projects</SelectItem>
                  {getVisibleProjectsForFilter().map(p => (
                    <SelectItem key={p.id} value={p.id}>{p.title}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Date Filter */}
            <div className="flex-1 min-w-0">
              <label className="block text-xs font-medium text-gray-600 mb-1">Date Range</label>
              <Select value={dateFilter} onValueChange={setDateFilter}>
                <SelectTrigger className="w-full">
                  <Calendar className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="All Time" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Time</SelectItem>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="last7days">Last 7 Days</SelectItem>
                  <SelectItem value="custom">Custom Range</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Custom Date Range */}
            {dateFilter === 'custom' && (
              <div className="flex-1 min-w-0">
                <label className="block text-xs font-medium text-gray-600 mb-1">Custom Range</label>
                <div className="flex items-center space-x-2">
                  <Input
                    type="date"
                    value={customStartDate}
                    onChange={(e) => setCustomStartDate(e.target.value)}
                    className="flex-1"
                    placeholder="Start date"
                  />
                  <span className="text-gray-400 text-sm">to</span>
                  <Input
                    type="date"
                    value={customEndDate}
                    onChange={(e) => setCustomEndDate(e.target.value)}
                    className="flex-1"
                    placeholder="End date"
                  />
                </div>
              </div>
            )}

            {/* Add Log Button */}
            {user?.role !== 'client' && (
              <div className="flex-shrink-0">
                <label className="block text-xs font-medium text-transparent mb-1">Action</label>
                <Button onClick={() => setShowForm(!showForm)} className="gradient-primary text-white w-full lg:w-auto">
                  <Plus className="mr-2 h-4 w-4" /> Add Log
                </Button>
              </div>
            )}
          </div>

          {/* Active Filters Display */}
          {hasActiveFilters && (
            <div className="flex flex-wrap gap-2 pt-2 border-t">
              <span className="text-xs text-gray-500">Active filters:</span>
              {projectIdFilter !== 'all' && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                  Project: {getVisibleProjectsForFilter().find(p => p.id === projectIdFilter)?.title || 'Unknown'}
                  <button
                    onClick={() => setProjectIdFilter('all')}
                    className="ml-1 hover:text-blue-600"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </span>
              )}
              {dateFilter !== 'all' && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                  Date: {dateFilter === 'custom' ? `${customStartDate} to ${customEndDate}` : dateFilter}
                  <button
                    onClick={() => {
                      setDateFilter('all');
                      setCustomStartDate('');
                      setCustomEndDate('');
                    }}
                    className="ml-1 hover:text-green-600"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </span>
              )}
            </div>
          )}
        </div>
      </motion.div>

      {showForm && (
        <DailyLogForm 
          projects={projects} 
          onSubmit={handleFormSubmit} 
          onCancel={() => setShowForm(false)}
          initialProjectId={projectIdFilter}
        />
      )}

      {/* Results Summary */}
      <div className="flex items-center justify-between text-sm text-gray-600 bg-gray-50 px-4 py-2 rounded-lg">
        <span>
          Showing {filteredDailyLogs.length} of {dailyLogs.length} daily logs
          {hasActiveFilters && ' (filtered)'}
        </span>
        {filteredDailyLogs.length > 0 && (
          <span className="text-xs">
            Latest: {new Date(filteredDailyLogs[0].timestamp || filteredDailyLogs[0].createdAt).toLocaleDateString()}
          </span>
        )}
      </div>

      <div className="space-y-4">
        {filteredDailyLogs.length > 0 ? (
          filteredDailyLogs.map((log, index) => {
            const project = projects.find(p => p.id === log.projectId);
            return (
              <DailyLogItem 
                key={log.id}
                log={log}
                project={project}
                index={index}
                onDeleteLog={handleDeleteLogMessage}
              />
            );
          })
        ) : (
          <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ delay: 0.3 }} className="text-center py-12">
            <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center"><FileText className="h-12 w-12 text-gray-400" /></div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No daily logs yet</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">{user?.role === 'client' ? 'No daily logs have been shared with you yet.' : 'Start by adding your first project daily log.'}</p>
            {user?.role !== 'client' && (<Button onClick={() => setShowForm(true)} className="gradient-primary text-white"><Plus className="mr-2 h-4 w-4" />Add First Log</Button>)}
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default DailyLogPage;